<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Attendance System</title>
    <style>
        * {
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1.6;
        }
        .container {
            max-width: 500px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 40px 30px;
            text-align: center;
        }
        .icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.8rem;
        }
        p {
            color: #666;
            margin-bottom: 25px;
            font-size: 1.1rem;
        }
        .status {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #dc3545;
        }
        .retry-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        .retry-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }
        .offline-features {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            color: #0c5460;
            padding: 20px;
            border-radius: 10px;
            margin: 25px 0;
            border-left: 4px solid #17a2b8;
            text-align: left;
        }
        .offline-features h3 {
            margin-top: 0;
            color: #0c5460;
        }
        .offline-features ul {
            margin: 15px 0;
            padding-left: 20px;
        }
        .offline-features li {
            margin-bottom: 8px;
        }
        .connection-status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 8px;
            font-weight: 600;
        }
        .online {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .offline {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📱</div>
        <h1>You're Offline</h1>
        <p>The attendance system is currently offline, but you can still use some features.</p>
        
        <div class="connection-status" id="connectionStatus">
            <span id="statusText">🔴 No internet connection</span>
        </div>

        <div class="status">
            <strong>⚠️ Limited Functionality:</strong><br>
            Some features require an internet connection and will sync when you're back online.
        </div>

        <div class="offline-features">
            <h3>📋 Available Offline Features:</h3>
            <ul>
                <li>🔍 Device fingerprint collection continues</li>
                <li>💾 Attendance data stored locally</li>
                <li>📱 Touch pattern analysis active</li>
                <li>🔄 Auto-sync when connection restored</li>
                <li>📊 View cached attendance data</li>
            </ul>
        </div>

        <button class="retry-btn" onclick="checkConnection()">
            🔄 Check Connection
        </button>
        
        <button class="retry-btn" onclick="goToApp()" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);">
            📱 Continue Offline
        </button>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            const statusText = document.getElementById('statusText');
            
            if (navigator.onLine) {
                statusElement.className = 'connection-status online';
                statusText.textContent = '🟢 Connection restored!';
                
                // Auto-redirect after 2 seconds if online
                setTimeout(() => {
                    window.location.href = '/attendance/';
                }, 2000);
            } else {
                statusElement.className = 'connection-status offline';
                statusText.textContent = '🔴 No internet connection';
            }
        }

        // Check connection manually
        function checkConnection() {
            updateConnectionStatus();
            
            if (navigator.onLine) {
                // Try to fetch a small resource to verify real connectivity
                fetch('/attendance/ping.php', { 
                    method: 'HEAD',
                    cache: 'no-cache'
                })
                .then(() => {
                    window.location.href = '/attendance/';
                })
                .catch(() => {
                    alert('Connection appears restored but server is unreachable. Please try again.');
                });
            } else {
                alert('Still offline. Please check your internet connection.');
            }
        }

        // Go to app in offline mode
        function goToApp() {
            window.location.href = '/attendance/';
        }

        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Initial status check
        updateConnectionStatus();

        // Periodic connection check
        setInterval(updateConnectionStatus, 5000);

        // Register service worker if not already registered
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/attendance/sw.js')
                .then((registration) => {
                    console.log('✅ Service Worker registered from offline page');
                })
                .catch((error) => {
                    console.error('❌ Service Worker registration failed:', error);
                });
        }
    </script>
</body>
</html>
