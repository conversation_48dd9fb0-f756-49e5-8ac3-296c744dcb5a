<?php
require_once('db.php');
date_default_timezone_set('Asia/Thimphu');

header('Content-Type: application/json');

// Debug logging function
function debug_log($message) {
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents('debug_log.txt', "[$timestamp] $message\n", FILE_APPEND);
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'error' => 'Invalid JSON input']);
    exit;
}

$fingerprint = $input['fingerprint'] ?? null;
$sessionToken = $input['sessionToken'] ?? null;
$staffId = $input['staffId'] ?? null;

if (!$fingerprint) {
    echo json_encode(['success' => false, 'error' => 'No fingerprint data provided']);
    exit;
}

$deviceSignatureHash = $fingerprint['deviceSignatureHash'] ?? '';
$clientIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

debug_log("verify_fingerprint.php - Verifying DSH: $deviceSignatureHash, IP: $clientIP, Staff: $staffId");

try {
    // Verify session token if provided
    if ($sessionToken) {
        $sessionStmt = $conn->prepare("
            SELECT client_ip, created_at, expires_at 
            FROM scan_sessions 
            WHERE session_token = ? AND expires_at > NOW()
        ");
        $sessionStmt->bind_param("s", $sessionToken);
        $sessionStmt->execute();
        $session = $sessionStmt->get_result()->fetch_assoc();

        if (!$session) {
            echo json_encode(['success' => false, 'error' => 'Invalid or expired session token']);
            exit;
        }

        // Verify IP matches (allow local network range)
        $sessionIP = $session['client_ip'];
        if ($sessionIP !== $clientIP && !isLocalNetworkIP($clientIP, $sessionIP)) {
            debug_log("verify_fingerprint.php - IP mismatch: session=$sessionIP, current=$clientIP");
            echo json_encode(['success' => false, 'error' => 'IP address mismatch']);
            exit;
        }
    }

    // Look up device fingerprint
    $fpStmt = $conn->prepare("
        SELECT id, fingerprint_data, user_info, trust_score, first_seen, last_seen 
        FROM device_fingerprints 
        WHERE device_signature_hash = ? AND is_active = TRUE
    ");
    $fpStmt->bind_param("s", $deviceSignatureHash);
    $fpStmt->execute();
    $deviceRecord = $fpStmt->get_result()->fetch_assoc();

    if (!$deviceRecord) {
        echo json_encode([
            'success' => false, 
            'error' => 'Device not recognized',
            'action' => 'register_required'
        ]);
        exit;
    }

    // Parse stored fingerprint for comparison
    $storedFingerprint = json_decode($deviceRecord['fingerprint_data'], true);
    
    // Calculate similarity score
    $similarityScore = calculateFingerprintSimilarity($fingerprint, $storedFingerprint);
    
    debug_log("verify_fingerprint.php - Similarity score: $similarityScore%, Trust: {$deviceRecord['trust_score']}");

    // Determine if verification passes
    $minSimilarity = 70; // Minimum 70% similarity required
    $minTrustScore = 5.0; // Minimum trust score
    
    $verificationPassed = (
        $similarityScore >= $minSimilarity && 
        $deviceRecord['trust_score'] >= $minTrustScore
    );

    if ($verificationPassed) {
        // Update last seen and increase trust
        $newTrustScore = min(100.0, $deviceRecord['trust_score'] + 0.5);
        $updateStmt = $conn->prepare("
            UPDATE device_fingerprints 
            SET last_seen = CURRENT_TIMESTAMP, trust_score = ? 
            WHERE id = ?
        ");
        $updateStmt->bind_param("di", $newTrustScore, $deviceRecord['id']);
        $updateStmt->execute();

        // If staff ID provided, link device to staff member
        if ($staffId) {
            linkDeviceToStaff($conn, $deviceRecord['id'], $staffId);
        }

        echo json_encode([
            'success' => true,
            'device_id' => $deviceRecord['id'],
            'trust_score' => $newTrustScore,
            'similarity_score' => $similarityScore,
            'first_seen' => $deviceRecord['first_seen'],
            'verification_level' => getTrustLevel($newTrustScore)
        ]);
    } else {
        // Verification failed - decrease trust score
        $newTrustScore = max(0.0, $deviceRecord['trust_score'] - 2.0);
        $updateStmt = $conn->prepare("
            UPDATE device_fingerprints 
            SET trust_score = ? 
            WHERE id = ?
        ");
        $updateStmt->bind_param("di", $newTrustScore, $deviceRecord['id']);
        $updateStmt->execute();

        echo json_encode([
            'success' => false,
            'error' => 'Device verification failed',
            'similarity_score' => $similarityScore,
            'trust_score' => $newTrustScore,
            'required_similarity' => $minSimilarity,
            'action' => $similarityScore < 50 ? 'device_changed' : 'retry_later'
        ]);
    }

} catch (Exception $e) {
    debug_log("verify_fingerprint.php - Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}

// Helper function to check if IPs are in same local network
function isLocalNetworkIP($ip1, $ip2) {
    // Simple check for same subnet (first 3 octets)
    $parts1 = explode('.', $ip1);
    $parts2 = explode('.', $ip2);
    
    if (count($parts1) !== 4 || count($parts2) !== 4) {
        return false;
    }
    
    return ($parts1[0] === $parts2[0] && $parts1[1] === $parts2[1] && $parts1[2] === $parts2[2]);
}

// Calculate fingerprint similarity
function calculateFingerprintSimilarity($fp1, $fp2) {
    $score = 0;
    $maxScore = 0;

    // Compare device signature hash (most important - 40 points)
    $maxScore += 40;
    if (isset($fp1['deviceSignatureHash']) && isset($fp2['deviceSignatureHash'])) {
        if ($fp1['deviceSignatureHash'] === $fp2['deviceSignatureHash']) {
            $score += 40;
        }
    }

    // Compare canvas fingerprint (30 points)
    $maxScore += 30;
    if (isset($fp1['canvas']) && isset($fp2['canvas'])) {
        if ($fp1['canvas'] === $fp2['canvas']) {
            $score += 30;
        }
    }

    // Compare screen resolution (15 points)
    $maxScore += 15;
    if (isset($fp1['screen']) && isset($fp2['screen'])) {
        if ($fp1['screen']['width'] === $fp2['screen']['width'] && 
            $fp1['screen']['height'] === $fp2['screen']['height']) {
            $score += 15;
        }
    }

    // Compare user agent (10 points)
    $maxScore += 10;
    if (isset($fp1['basic']['userAgent']) && isset($fp2['basic']['userAgent'])) {
        if ($fp1['basic']['userAgent'] === $fp2['basic']['userAgent']) {
            $score += 10;
        }
    }

    // Compare timezone (5 points)
    $maxScore += 5;
    if (isset($fp1['locale']['timezone']) && isset($fp2['locale']['timezone'])) {
        if ($fp1['locale']['timezone'] === $fp2['locale']['timezone']) {
            $score += 5;
        }
    }

    return $maxScore > 0 ? round(($score / $maxScore) * 100, 2) : 0;
}

// Get trust level description
function getTrustLevel($trustScore) {
    if ($trustScore >= 80) return 'high';
    if ($trustScore >= 50) return 'medium';
    if ($trustScore >= 20) return 'low';
    return 'very_low';
}

// Link device to staff member
function linkDeviceToStaff($conn, $deviceId, $staffId) {
    try {
        // Create device_staff_links table if it doesn't exist
        $createTable = "
        CREATE TABLE IF NOT EXISTS device_staff_links (
            id INT AUTO_INCREMENT PRIMARY KEY,
            device_id INT NOT NULL,
            staff_id INT NOT NULL,
            linked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_primary BOOLEAN DEFAULT FALSE,
            UNIQUE KEY unique_device_staff (device_id, staff_id),
            FOREIGN KEY (device_id) REFERENCES device_fingerprints(id) ON DELETE CASCADE
        )";
        $conn->query($createTable);

        // Insert or update link
        $linkStmt = $conn->prepare("
            INSERT INTO device_staff_links (device_id, staff_id, is_primary) 
            VALUES (?, ?, TRUE) 
            ON DUPLICATE KEY UPDATE linked_at = CURRENT_TIMESTAMP, is_primary = TRUE
        ");
        $linkStmt->bind_param("ii", $deviceId, $staffId);
        $linkStmt->execute();

        debug_log("verify_fingerprint.php - Linked device $deviceId to staff $staffId");
    } catch (Exception $e) {
        debug_log("verify_fingerprint.php - Link error: " . $e->getMessage());
    }
}
?>
