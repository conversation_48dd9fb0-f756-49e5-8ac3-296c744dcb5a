/**
 * Touch Pattern and Behavioral Biometrics Authentication
 * Analyzes user interaction patterns for additional security layer
 */

class BiometricAuth {
    constructor() {
        this.touchPatterns = [];
        this.swipePatterns = [];
        this.tapRhythms = [];
        this.keyboardPatterns = [];
        this.scrollBehavior = [];
        this.isRecording = false;
        this.userProfile = null;
        this.confidenceThreshold = 0.7; // 70% confidence required
    }

    // Initialize biometric tracking
    init() {
        console.log('🔐 Initializing biometric authentication...');
        
        this.setupTouchTracking();
        this.setupSwipeTracking();
        this.setupKeyboardTracking();
        this.setupScrollTracking();
        this.setupMouseTracking();
        
        // Load existing user profile if available
        this.loadUserProfile();
        
        console.log('✅ Biometric authentication initialized');
    }

    // Setup touch event tracking
    setupTouchTracking() {
        let touchStartTime = 0;
        let touchStartPos = { x: 0, y: 0 };
        let touchSequence = [];

        document.addEventListener('touchstart', (e) => {
            if (!this.isRecording) return;
            
            touchStartTime = Date.now();
            const touch = e.touches[0];
            touchStartPos = { x: touch.clientX, y: touch.clientY };
            
            touchSequence.push({
                type: 'start',
                x: touch.clientX,
                y: touch.clientY,
                pressure: touch.force || 0,
                timestamp: touchStartTime,
                radiusX: touch.radiusX || 0,
                radiusY: touch.radiusY || 0
            });
        });

        document.addEventListener('touchmove', (e) => {
            if (!this.isRecording) return;
            
            const touch = e.touches[0];
            touchSequence.push({
                type: 'move',
                x: touch.clientX,
                y: touch.clientY,
                pressure: touch.force || 0,
                timestamp: Date.now(),
                radiusX: touch.radiusX || 0,
                radiusY: touch.radiusY || 0
            });
        });

        document.addEventListener('touchend', (e) => {
            if (!this.isRecording) return;
            
            const duration = Date.now() - touchStartTime;
            const endTouch = e.changedTouches[0];
            
            touchSequence.push({
                type: 'end',
                x: endTouch.clientX,
                y: endTouch.clientY,
                timestamp: Date.now(),
                duration: duration
            });

            // Analyze touch pattern
            this.analyzeTouchPattern(touchSequence);
            
            // Record tap rhythm
            this.tapRhythms.push({
                duration: duration,
                timestamp: Date.now(),
                startPos: touchStartPos,
                endPos: { x: endTouch.clientX, y: endTouch.clientY }
            });

            // Keep only recent patterns
            if (this.tapRhythms.length > 20) {
                this.tapRhythms = this.tapRhythms.slice(-20);
            }

            touchSequence = [];
        });
    }

    // Setup swipe gesture tracking
    setupSwipeTracking() {
        let swipeStart = null;
        let swipePoints = [];

        document.addEventListener('touchstart', (e) => {
            if (!this.isRecording) return;
            
            const touch = e.touches[0];
            swipeStart = {
                x: touch.clientX,
                y: touch.clientY,
                timestamp: Date.now()
            };
            swipePoints = [swipeStart];
        });

        document.addEventListener('touchmove', (e) => {
            if (!this.isRecording || !swipeStart) return;
            
            const touch = e.touches[0];
            swipePoints.push({
                x: touch.clientX,
                y: touch.clientY,
                timestamp: Date.now()
            });
        });

        document.addEventListener('touchend', (e) => {
            if (!this.isRecording || !swipeStart || swipePoints.length < 3) return;
            
            const swipeEnd = {
                x: e.changedTouches[0].clientX,
                y: e.changedTouches[0].clientY,
                timestamp: Date.now()
            };

            const swipeData = this.analyzeSwipePattern(swipeStart, swipeEnd, swipePoints);
            if (swipeData) {
                this.swipePatterns.push(swipeData);
                
                // Keep only recent patterns
                if (this.swipePatterns.length > 15) {
                    this.swipePatterns = this.swipePatterns.slice(-15);
                }
            }

            swipeStart = null;
            swipePoints = [];
        });
    }

    // Setup keyboard pattern tracking
    setupKeyboardTracking() {
        let keySequence = [];
        let lastKeyTime = 0;

        document.addEventListener('keydown', (e) => {
            if (!this.isRecording) return;
            
            const currentTime = Date.now();
            const timeBetweenKeys = currentTime - lastKeyTime;
            
            keySequence.push({
                key: e.key,
                code: e.code,
                timestamp: currentTime,
                timeBetween: timeBetweenKeys,
                shiftKey: e.shiftKey,
                ctrlKey: e.ctrlKey,
                altKey: e.altKey
            });

            lastKeyTime = currentTime;

            // Analyze typing rhythm for sequences of 5+ keys
            if (keySequence.length >= 5) {
                this.analyzeTypingRhythm(keySequence.slice(-5));
            }

            // Keep only recent keys
            if (keySequence.length > 50) {
                keySequence = keySequence.slice(-50);
            }
        });
    }

    // Setup scroll behavior tracking
    setupScrollTracking() {
        let scrollData = [];
        let lastScrollTime = 0;

        window.addEventListener('scroll', (e) => {
            if (!this.isRecording) return;
            
            const currentTime = Date.now();
            const scrollY = window.scrollY;
            const timeBetweenScrolls = currentTime - lastScrollTime;

            scrollData.push({
                scrollY: scrollY,
                timestamp: currentTime,
                timeBetween: timeBetweenScrolls,
                velocity: timeBetweenScrolls > 0 ? Math.abs(scrollY - (scrollData[scrollData.length - 1]?.scrollY || 0)) / timeBetweenScrolls : 0
            });

            lastScrollTime = currentTime;

            // Keep only recent scroll data
            if (scrollData.length > 30) {
                scrollData = scrollData.slice(-30);
            }

            this.scrollBehavior = scrollData;
        });
    }

    // Setup mouse tracking for desktop users
    setupMouseTracking() {
        let mouseSequence = [];
        let clickStartTime = 0;

        document.addEventListener('mousedown', (e) => {
            if (!this.isRecording) return;
            
            clickStartTime = Date.now();
            mouseSequence.push({
                type: 'mousedown',
                x: e.clientX,
                y: e.clientY,
                button: e.button,
                timestamp: clickStartTime
            });
        });

        document.addEventListener('mouseup', (e) => {
            if (!this.isRecording) return;
            
            const duration = Date.now() - clickStartTime;
            mouseSequence.push({
                type: 'mouseup',
                x: e.clientX,
                y: e.clientY,
                button: e.button,
                timestamp: Date.now(),
                duration: duration
            });

            // Treat mouse clicks similar to taps
            this.tapRhythms.push({
                duration: duration,
                timestamp: Date.now(),
                startPos: { x: e.clientX, y: e.clientY },
                endPos: { x: e.clientX, y: e.clientY },
                type: 'mouse'
            });
        });

        document.addEventListener('mousemove', (e) => {
            if (!this.isRecording) return;
            
            // Sample mouse movement (not every pixel to avoid too much data)
            if (Math.random() < 0.1) { // 10% sampling rate
                mouseSequence.push({
                    type: 'mousemove',
                    x: e.clientX,
                    y: e.clientY,
                    timestamp: Date.now()
                });
            }

            // Keep only recent mouse data
            if (mouseSequence.length > 100) {
                mouseSequence = mouseSequence.slice(-100);
            }
        });
    }

    // Analyze touch pattern characteristics
    analyzeTouchPattern(sequence) {
        if (sequence.length < 2) return;

        const pattern = {
            duration: sequence[sequence.length - 1].timestamp - sequence[0].timestamp,
            points: sequence.length,
            avgPressure: sequence.reduce((sum, point) => sum + (point.pressure || 0), 0) / sequence.length,
            maxPressure: Math.max(...sequence.map(point => point.pressure || 0)),
            path: this.calculateTouchPath(sequence),
            timestamp: Date.now()
        };

        this.touchPatterns.push(pattern);

        // Keep only recent patterns
        if (this.touchPatterns.length > 25) {
            this.touchPatterns = this.touchPatterns.slice(-25);
        }
    }

    // Calculate touch path characteristics
    calculateTouchPath(sequence) {
        if (sequence.length < 2) return { distance: 0, direction: 0, smoothness: 0 };

        let totalDistance = 0;
        let directions = [];
        
        for (let i = 1; i < sequence.length; i++) {
            const prev = sequence[i - 1];
            const curr = sequence[i];
            
            const dx = curr.x - prev.x;
            const dy = curr.y - prev.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            const direction = Math.atan2(dy, dx);
            
            totalDistance += distance;
            directions.push(direction);
        }

        // Calculate smoothness (consistency of direction changes)
        let directionChanges = 0;
        for (let i = 1; i < directions.length; i++) {
            const change = Math.abs(directions[i] - directions[i - 1]);
            directionChanges += Math.min(change, 2 * Math.PI - change);
        }

        return {
            distance: totalDistance,
            avgDirection: directions.reduce((sum, dir) => sum + dir, 0) / directions.length,
            smoothness: directions.length > 1 ? 1 - (directionChanges / (directions.length - 1)) / Math.PI : 1
        };
    }

    // Analyze swipe pattern
    analyzeSwipePattern(start, end, points) {
        const dx = end.x - start.x;
        const dy = end.y - start.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const duration = end.timestamp - start.timestamp;
        
        // Only consider significant swipes
        if (distance < 50 || duration < 100) return null;

        const velocity = distance / duration;
        const direction = Math.atan2(dy, dx);
        
        // Calculate acceleration pattern
        const accelerations = [];
        for (let i = 2; i < points.length; i++) {
            const p1 = points[i - 2];
            const p2 = points[i - 1];
            const p3 = points[i];
            
            const v1 = Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2)) / (p2.timestamp - p1.timestamp);
            const v2 = Math.sqrt(Math.pow(p3.x - p2.x, 2) + Math.pow(p3.y - p2.y, 2)) / (p3.timestamp - p2.timestamp);
            
            accelerations.push(v2 - v1);
        }

        return {
            distance: distance,
            duration: duration,
            velocity: velocity,
            direction: direction,
            accelerationPattern: accelerations,
            timestamp: Date.now()
        };
    }

    // Analyze typing rhythm
    analyzeTypingRhythm(keySequence) {
        const rhythmData = {
            avgTimeBetweenKeys: keySequence.reduce((sum, key) => sum + key.timeBetween, 0) / keySequence.length,
            rhythmVariation: this.calculateVariation(keySequence.map(key => key.timeBetween)),
            timestamp: Date.now()
        };

        this.keyboardPatterns.push(rhythmData);

        // Keep only recent patterns
        if (this.keyboardPatterns.length > 20) {
            this.keyboardPatterns = this.keyboardPatterns.slice(-20);
        }
    }

    // Calculate variation in timing
    calculateVariation(values) {
        if (values.length < 2) return 0;
        
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
        
        return Math.sqrt(variance);
    }

    // Start recording biometric data
    startRecording() {
        this.isRecording = true;
        console.log('🎯 Started biometric recording');
    }

    // Stop recording biometric data
    stopRecording() {
        this.isRecording = false;
        console.log('⏹️ Stopped biometric recording');
    }

    // Get current biometric profile
    getBiometricProfile() {
        return {
            touchPatterns: this.touchPatterns.slice(-10), // Last 10 patterns
            swipePatterns: this.swipePatterns.slice(-5),  // Last 5 swipes
            tapRhythms: this.tapRhythms.slice(-10),       // Last 10 taps
            keyboardPatterns: this.keyboardPatterns.slice(-5), // Last 5 typing patterns
            scrollBehavior: this.scrollBehavior.slice(-10),    // Last 10 scroll events
            timestamp: Date.now()
        };
    }

    // Save user profile for future comparison
    saveUserProfile() {
        const profile = this.getBiometricProfile();
        this.userProfile = profile;
        
        try {
            localStorage.setItem('biometricProfile', JSON.stringify(profile));
            console.log('💾 Biometric profile saved');
        } catch (error) {
            console.warn('Failed to save biometric profile:', error);
        }
    }

    // Load existing user profile
    loadUserProfile() {
        try {
            const stored = localStorage.getItem('biometricProfile');
            if (stored) {
                this.userProfile = JSON.parse(stored);
                console.log('📱 Loaded existing biometric profile');
            }
        } catch (error) {
            console.warn('Failed to load biometric profile:', error);
        }
    }

    // Compare current behavior with stored profile
    verifyBiometrics() {
        if (!this.userProfile) {
            console.log('⚠️ No stored profile for comparison');
            return { confidence: 0, match: false, reason: 'no_profile' };
        }

        const currentProfile = this.getBiometricProfile();
        let totalScore = 0;
        let maxScore = 0;

        // Compare tap rhythms
        if (this.userProfile.tapRhythms.length > 0 && currentProfile.tapRhythms.length > 0) {
            const tapScore = this.compareTapRhythms(this.userProfile.tapRhythms, currentProfile.tapRhythms);
            totalScore += tapScore * 0.3; // 30% weight
            maxScore += 0.3;
        }

        // Compare touch patterns
        if (this.userProfile.touchPatterns.length > 0 && currentProfile.touchPatterns.length > 0) {
            const touchScore = this.compareTouchPatterns(this.userProfile.touchPatterns, currentProfile.touchPatterns);
            totalScore += touchScore * 0.25; // 25% weight
            maxScore += 0.25;
        }

        // Compare swipe patterns
        if (this.userProfile.swipePatterns.length > 0 && currentProfile.swipePatterns.length > 0) {
            const swipeScore = this.compareSwipePatterns(this.userProfile.swipePatterns, currentProfile.swipePatterns);
            totalScore += swipeScore * 0.2; // 20% weight
            maxScore += 0.2;
        }

        // Compare keyboard patterns
        if (this.userProfile.keyboardPatterns.length > 0 && currentProfile.keyboardPatterns.length > 0) {
            const keyboardScore = this.compareKeyboardPatterns(this.userProfile.keyboardPatterns, currentProfile.keyboardPatterns);
            totalScore += keyboardScore * 0.15; // 15% weight
            maxScore += 0.15;
        }

        // Compare scroll behavior
        if (this.userProfile.scrollBehavior.length > 0 && currentProfile.scrollBehavior.length > 0) {
            const scrollScore = this.compareScrollBehavior(this.userProfile.scrollBehavior, currentProfile.scrollBehavior);
            totalScore += scrollScore * 0.1; // 10% weight
            maxScore += 0.1;
        }

        const confidence = maxScore > 0 ? totalScore / maxScore : 0;
        const match = confidence >= this.confidenceThreshold;

        console.log(`🔍 Biometric verification: ${(confidence * 100).toFixed(1)}% confidence`);

        return {
            confidence: confidence,
            match: match,
            reason: match ? 'verified' : 'low_confidence',
            details: {
                totalScore: totalScore,
                maxScore: maxScore,
                threshold: this.confidenceThreshold
            }
        };
    }

    // Compare tap rhythm patterns
    compareTapRhythms(stored, current) {
        const storedAvg = stored.reduce((sum, tap) => sum + tap.duration, 0) / stored.length;
        const currentAvg = current.reduce((sum, tap) => sum + tap.duration, 0) / current.length;
        
        const difference = Math.abs(storedAvg - currentAvg);
        const maxDifference = Math.max(storedAvg, currentAvg);
        
        return maxDifference > 0 ? Math.max(0, 1 - (difference / maxDifference)) : 1;
    }

    // Compare touch pattern characteristics
    compareTouchPatterns(stored, current) {
        // Compare average pressure, duration, and path characteristics
        const storedAvgPressure = stored.reduce((sum, pattern) => sum + pattern.avgPressure, 0) / stored.length;
        const currentAvgPressure = current.reduce((sum, pattern) => sum + pattern.avgPressure, 0) / current.length;
        
        const pressureDiff = Math.abs(storedAvgPressure - currentAvgPressure);
        const pressureScore = Math.max(0, 1 - pressureDiff);
        
        return pressureScore;
    }

    // Compare swipe pattern characteristics
    compareSwipePatterns(stored, current) {
        const storedAvgVelocity = stored.reduce((sum, swipe) => sum + swipe.velocity, 0) / stored.length;
        const currentAvgVelocity = current.reduce((sum, swipe) => sum + swipe.velocity, 0) / current.length;
        
        const velocityDiff = Math.abs(storedAvgVelocity - currentAvgVelocity);
        const maxVelocity = Math.max(storedAvgVelocity, currentAvgVelocity);
        
        return maxVelocity > 0 ? Math.max(0, 1 - (velocityDiff / maxVelocity)) : 1;
    }

    // Compare keyboard typing patterns
    compareKeyboardPatterns(stored, current) {
        const storedAvgTiming = stored.reduce((sum, pattern) => sum + pattern.avgTimeBetweenKeys, 0) / stored.length;
        const currentAvgTiming = current.reduce((sum, pattern) => sum + pattern.avgTimeBetweenKeys, 0) / current.length;
        
        const timingDiff = Math.abs(storedAvgTiming - currentAvgTiming);
        const maxTiming = Math.max(storedAvgTiming, currentAvgTiming);
        
        return maxTiming > 0 ? Math.max(0, 1 - (timingDiff / maxTiming)) : 1;
    }

    // Compare scroll behavior patterns
    compareScrollBehavior(stored, current) {
        const storedAvgVelocity = stored.reduce((sum, scroll) => sum + scroll.velocity, 0) / stored.length;
        const currentAvgVelocity = current.reduce((sum, scroll) => sum + scroll.velocity, 0) / current.length;
        
        const velocityDiff = Math.abs(storedAvgVelocity - currentAvgVelocity);
        const maxVelocity = Math.max(storedAvgVelocity, currentAvgVelocity);
        
        return maxVelocity > 0 ? Math.max(0, 1 - (velocityDiff / maxVelocity)) : 1;
    }
}

// Global instance
window.BiometricAuth = BiometricAuth;
