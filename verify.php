<?php
header('Content-Type: application/json');

$data = json_decode(file_get_contents("php://input"), true);
$staff_id = intval($data['staff_id']);
$device_token = $data['device_token'] ?? '';
$phone_model = $data['phone_model'] ?? '';

$conn = new mysqli("localhost", "root", "", "attendance_system");
if ($conn->connect_error) {
    echo json_encode(["success" => false, "message" => "Database connection failed."]);
    exit;
}

$stmt = $conn->prepare("SELECT full_name, eid_cid_permit, phone_model, device_token FROM staff WHERE id = ?");
$stmt->bind_param("i", $staff_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if ($user) {
    // Register device if empty
    if (empty($user['phone_model']) || empty($user['device_token'])) {
        $updateStmt = $conn->prepare("UPDATE staff SET phone_model = ?, device_token = ? WHERE id = ?");
        $updateStmt->bind_param("ssi", $phone_model, $device_token, $staff_id);
        $updateStmt->execute();
        $updateStmt->close();

        echo json_encode([
            "success" => true,
            "name" => $user['full_name'],
            "eid" => $user['eid_cid_permit'],
            "message" => "Device registered successfully."
        ]);
    } else {
        // Validate match
        if ($user['phone_model'] === $phone_model && $user['device_token'] === $device_token) {
            echo json_encode([
                "success" => true,
                "name" => $user['full_name'],
                "eid" => $user['eid_cid_permit']
            ]);
        } else {
            echo json_encode(["success" => false, "message" => "Device or phone model does not match registered device."]);
        }
    }
} else {
    echo json_encode(["success" => false, "message" => "Staff ID not found."]);
}

$stmt->close();
$conn->close();
?>
