<?php
require_once('db.php');
date_default_timezone_set('Asia/Thimphu');

header('Content-Type: application/json');

// Debug logging function
function debug_log($message) {
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents('debug_log.txt', "[$timestamp] $message\n", FILE_APPEND);
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'error' => 'Invalid JSON input']);
    exit;
}

$fingerprint = $input['fingerprint'] ?? null;
$userInfo = $input['userInfo'] ?? null;
$sessionToken = $input['sessionToken'] ?? null;

if (!$fingerprint) {
    echo json_encode(['success' => false, 'error' => 'No fingerprint data provided']);
    exit;
}

$deviceSignatureHash = $fingerprint['deviceSignatureHash'] ?? '';
$clientIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

debug_log("save_fingerprint.php - Saving fingerprint for DSH: $deviceSignatureHash, IP: $clientIP");

try {
    // Create device_fingerprints table if it doesn't exist
    $createTable = "
    CREATE TABLE IF NOT EXISTS device_fingerprints (
        id INT AUTO_INCREMENT PRIMARY KEY,
        device_signature_hash VARCHAR(255) UNIQUE NOT NULL,
        fingerprint_data JSON NOT NULL,
        user_info JSON,
        client_ip VARCHAR(45),
        session_token VARCHAR(255),
        first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        trust_score DECIMAL(5,2) DEFAULT 0.00,
        is_active BOOLEAN DEFAULT TRUE,
        INDEX idx_dsh (device_signature_hash),
        INDEX idx_ip (client_ip),
        INDEX idx_session (session_token)
    )";
    
    $conn->query($createTable);

    // Check if this device signature already exists
    $checkStmt = $conn->prepare("SELECT id, trust_score, first_seen FROM device_fingerprints WHERE device_signature_hash = ?");
    $checkStmt->bind_param("s", $deviceSignatureHash);
    $checkStmt->execute();
    $existing = $checkStmt->get_result()->fetch_assoc();

    $fingerprintJson = json_encode($fingerprint);
    $userInfoJson = $userInfo ? json_encode($userInfo) : null;

    if ($existing) {
        // Update existing fingerprint
        $newTrustScore = min(100.0, $existing['trust_score'] + 1.0); // Increase trust
        
        $updateStmt = $conn->prepare("
            UPDATE device_fingerprints 
            SET fingerprint_data = ?, 
                user_info = COALESCE(?, user_info),
                client_ip = ?, 
                session_token = ?, 
                trust_score = ?,
                last_seen = CURRENT_TIMESTAMP 
            WHERE device_signature_hash = ?
        ");
        $updateStmt->bind_param("ssssds", $fingerprintJson, $userInfoJson, $clientIP, $sessionToken, $newTrustScore, $deviceSignatureHash);
        $updateStmt->execute();

        debug_log("save_fingerprint.php - Updated existing device, trust score: $newTrustScore");
        
        echo json_encode([
            'success' => true,
            'action' => 'updated',
            'device_id' => $existing['id'],
            'trust_score' => $newTrustScore,
            'first_seen' => $existing['first_seen']
        ]);
    } else {
        // Insert new fingerprint
        $initialTrustScore = 10.0; // Starting trust score
        
        $insertStmt = $conn->prepare("
            INSERT INTO device_fingerprints 
            (device_signature_hash, fingerprint_data, user_info, client_ip, session_token, trust_score) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $insertStmt->bind_param("sssssd", $deviceSignatureHash, $fingerprintJson, $userInfoJson, $clientIP, $sessionToken, $initialTrustScore);
        $insertStmt->execute();

        $deviceId = $conn->insert_id;
        
        debug_log("save_fingerprint.php - Created new device ID: $deviceId, DSH: $deviceSignatureHash");
        
        echo json_encode([
            'success' => true,
            'action' => 'created',
            'device_id' => $deviceId,
            'trust_score' => $initialTrustScore,
            'first_seen' => date('Y-m-d H:i:s')
        ]);
    }

} catch (Exception $e) {
    debug_log("save_fingerprint.php - Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
