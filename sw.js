/**
 * Service Worker for Advanced Attendance System PWA
 * Provides offline functionality and caching for device fingerprints
 */

const CACHE_NAME = 'attendance-system-v1.0.0';
const OFFLINE_URL = '/attendance/offline.html';

// Files to cache for offline functionality
const CACHE_FILES = [
    '/attendance/',
    '/attendance/index.php',
    '/attendance/attendance.php',
    '/attendance/register_phone.php',
    '/attendance/js/device-fingerprint.js',
    '/attendance/manifest.json',
    '/attendance/offline.html',
    // Add CSS and other static assets
    '/attendance/icons/icon-192x192.png',
    '/attendance/icons/icon-512x512.png'
];

// Install event - cache essential files
self.addEventListener('install', (event) => {
    console.log('🔧 Service Worker installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then((cache) => {
                console.log('📦 Caching app shell files');
                return cache.addAll(CACHE_FILES);
            })
            .then(() => {
                console.log('✅ Service Worker installed successfully');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('❌ Service Worker installation failed:', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('🚀 Service Worker activating...');
    
    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('🗑️ Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('✅ Service Worker activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', (event) => {
    // Skip non-GET requests
    if (event.request.method !== 'GET') {
        return;
    }

    // Skip external requests
    if (!event.request.url.startsWith(self.location.origin)) {
        return;
    }

    event.respondWith(
        caches.match(event.request)
            .then((cachedResponse) => {
                // Return cached version if available
                if (cachedResponse) {
                    console.log('📱 Serving from cache:', event.request.url);
                    return cachedResponse;
                }

                // Try to fetch from network
                return fetch(event.request)
                    .then((response) => {
                        // Don't cache non-successful responses
                        if (!response || response.status !== 200 || response.type !== 'basic') {
                            return response;
                        }

                        // Clone the response for caching
                        const responseToCache = response.clone();

                        // Cache dynamic content selectively
                        if (shouldCache(event.request.url)) {
                            caches.open(CACHE_NAME)
                                .then((cache) => {
                                    cache.put(event.request, responseToCache);
                                });
                        }

                        return response;
                    })
                    .catch(() => {
                        // Network failed, try to serve offline page for navigation requests
                        if (event.request.mode === 'navigate') {
                            return caches.match(OFFLINE_URL);
                        }
                        
                        // For other requests, return a basic offline response
                        return new Response('Offline', {
                            status: 503,
                            statusText: 'Service Unavailable'
                        });
                    });
            })
    );
});

// Background sync for offline attendance data
self.addEventListener('sync', (event) => {
    console.log('🔄 Background sync triggered:', event.tag);
    
    if (event.tag === 'attendance-sync') {
        event.waitUntil(syncAttendanceData());
    }
    
    if (event.tag === 'fingerprint-sync') {
        event.waitUntil(syncFingerprintData());
    }
});

// Push notification handler
self.addEventListener('push', (event) => {
    console.log('📬 Push notification received');
    
    const options = {
        body: event.data ? event.data.text() : 'Attendance system notification',
        icon: '/attendance/icons/icon-192x192.png',
        badge: '/attendance/icons/icon-72x72.png',
        vibrate: [200, 100, 200],
        data: {
            url: '/attendance/'
        },
        actions: [
            {
                action: 'open',
                title: 'Open App'
            },
            {
                action: 'dismiss',
                title: 'Dismiss'
            }
        ]
    };

    event.waitUntil(
        self.registration.showNotification('Attendance System', options)
    );
});

// Notification click handler
self.addEventListener('notificationclick', (event) => {
    console.log('🔔 Notification clicked:', event.action);
    
    event.notification.close();

    if (event.action === 'open') {
        event.waitUntil(
            clients.openWindow('/attendance/')
        );
    }
});

// Helper function to determine if a request should be cached
function shouldCache(url) {
    // Cache PHP files for offline access
    if (url.includes('.php')) {
        return true;
    }
    
    // Cache static assets
    if (url.includes('.css') || url.includes('.js') || url.includes('.png') || url.includes('.jpg')) {
        return true;
    }
    
    // Don't cache API endpoints that change frequently
    if (url.includes('save_fingerprint.php') || url.includes('verify_fingerprint.php')) {
        return false;
    }
    
    return false;
}

// Sync attendance data when back online
async function syncAttendanceData() {
    try {
        console.log('🔄 Syncing offline attendance data...');
        
        // Get offline attendance data from IndexedDB
        const offlineData = await getOfflineAttendanceData();
        
        if (offlineData.length > 0) {
            for (const record of offlineData) {
                try {
                    const response = await fetch('/attendance/sync_attendance.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(record)
                    });
                    
                    if (response.ok) {
                        await removeOfflineAttendanceRecord(record.id);
                        console.log('✅ Synced attendance record:', record.id);
                    }
                } catch (error) {
                    console.error('❌ Failed to sync record:', record.id, error);
                }
            }
        }
        
        console.log('✅ Attendance data sync complete');
    } catch (error) {
        console.error('❌ Attendance sync failed:', error);
    }
}

// Sync fingerprint data when back online
async function syncFingerprintData() {
    try {
        console.log('🔄 Syncing offline fingerprint data...');
        
        // Get offline fingerprint data from IndexedDB
        const offlineFingerprints = await getOfflineFingerprintData();
        
        if (offlineFingerprints.length > 0) {
            for (const fingerprint of offlineFingerprints) {
                try {
                    const response = await fetch('/attendance/save_fingerprint.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(fingerprint)
                    });
                    
                    if (response.ok) {
                        await removeOfflineFingerprintRecord(fingerprint.id);
                        console.log('✅ Synced fingerprint:', fingerprint.deviceSignatureHash);
                    }
                } catch (error) {
                    console.error('❌ Failed to sync fingerprint:', fingerprint.id, error);
                }
            }
        }
        
        console.log('✅ Fingerprint data sync complete');
    } catch (error) {
        console.error('❌ Fingerprint sync failed:', error);
    }
}

// Placeholder functions for IndexedDB operations
// These would be implemented with actual IndexedDB code
async function getOfflineAttendanceData() {
    // Implementation would retrieve data from IndexedDB
    return [];
}

async function removeOfflineAttendanceRecord(id) {
    // Implementation would remove record from IndexedDB
    console.log('Removing offline attendance record:', id);
}

async function getOfflineFingerprintData() {
    // Implementation would retrieve data from IndexedDB
    return [];
}

async function removeOfflineFingerprintRecord(id) {
    // Implementation would remove record from IndexedDB
    console.log('Removing offline fingerprint record:', id);
}

console.log('🔧 Service Worker script loaded');
