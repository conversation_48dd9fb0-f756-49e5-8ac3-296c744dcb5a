<?php
require_once('phpqrcode/qrlib.php');

// Set timezone
date_default_timezone_set('Asia/Thimphu');

// Your staff identifier
$staff_id = "staff001"; // or numeric like 1

// Generate or get device token (here a random UUID generator function example)
function generateUniqueDeviceToken() {
    if (function_exists('com_create_guid') === true) {
        return trim(com_create_guid(), '{}');
    }
    // fallback to random string
    return bin2hex(random_bytes(16));
}

$device_token = generateUniqueDeviceToken();

// Set expiry time for the token (e.g., 1 hour from now)
$expires = time() + 3600;

// Build the full URL with parameters
$qr_data = "http://172.31.18.161/attendance/attendance.php?staff_id={$staff_id}&device_token={$device_token}&expires={$expires}";

// Name your QR code file (optional)
$qr_file = "qr_{$staff_id}.png";

// Generate and save the QR code with high error correction and size 10
QRcode::png($qr_data, $qr_file, QR_ECLEVEL_H, 10);

// Output the QR code image directly in the browser
header('Content-Type: image/png');
readfile($qr_file);
exit;
