<?php
// Simple script to create placeholder PWA icons
// This creates basic colored squares as placeholders

// Create icons directory
if (!file_exists('icons')) {
    mkdir('icons', 0777, true);
}

// Icon sizes needed for PWA
$sizes = [72, 96, 128, 144, 152, 192, 384, 512];

foreach ($sizes as $size) {
    $filename = "icons/icon-{$size}x{$size}.png";
    
    // Create a simple colored square as placeholder
    $image = imagecreate($size, $size);
    
    // Define colors
    $background = imagecolorallocate($image, 118, 75, 162); // Purple background
    $text_color = imagecolorallocate($image, 255, 255, 255); // White text
    
    // Fill background
    imagefill($image, 0, 0, $background);
    
    // Add text (attendance icon)
    $font_size = max(1, min(5, $size / 20));
    $text = "📋";
    
    // For larger icons, add more detail
    if ($size >= 192) {
        $text = "📋\nATT";
        // Add border
        $border_color = imagecolorallocate($image, 102, 110, 234);
        imagerectangle($image, 2, 2, $size-3, $size-3, $border_color);
        imagerectangle($image, 4, 4, $size-5, $size-5, $border_color);
    }
    
    // For very large icons, add even more detail
    if ($size >= 384) {
        // Add gradient effect by drawing multiple rectangles
        for ($i = 0; $i < $size/8; $i++) {
            $shade = imagecolorallocate($image, 
                min(255, 118 + $i*2), 
                min(255, 75 + $i*2), 
                min(255, 162 + $i*2)
            );
            imagerectangle($image, $i, $i, $size-1-$i, $size-1-$i, $shade);
        }
    }
    
    // Save the image
    imagepng($image, $filename);
    imagedestroy($image);
    
    echo "✅ Created $filename ({$size}x{$size})<br>";
}

// Create a simple favicon.ico
$favicon_image = imagecreate(32, 32);
$bg = imagecolorallocate($favicon_image, 118, 75, 162);
$fg = imagecolorallocate($favicon_image, 255, 255, 255);
imagefill($favicon_image, 0, 0, $bg);

// Simple attendance icon pattern
for ($i = 8; $i < 24; $i += 3) {
    imageline($favicon_image, 6, $i, 26, $i, $fg);
}
// Add checkmark
imageline($favicon_image, 8, 12, 12, 16, $fg);
imageline($favicon_image, 12, 16, 20, 8, $fg);

imagepng($favicon_image, 'favicon.png');
imagedestroy($favicon_image);

echo "✅ Created favicon.png (32x32)<br>";

// Create screenshots directory for PWA
if (!file_exists('screenshots')) {
    mkdir('screenshots', 0777, true);
}

// Create placeholder screenshots
$mobile_screenshot = imagecreate(540, 720);
$desktop_screenshot = imagecreate(1280, 720);

// Mobile screenshot
$mobile_bg = imagecolorallocate($mobile_screenshot, 102, 126, 234);
$mobile_text = imagecolorallocate($mobile_screenshot, 255, 255, 255);
imagefill($mobile_screenshot, 0, 0, $mobile_bg);
imagestring($mobile_screenshot, 5, 180, 350, "Attendance App", $mobile_text);
imagestring($mobile_screenshot, 3, 200, 380, "Mobile View", $mobile_text);
imagepng($mobile_screenshot, 'screenshots/mobile-screenshot.png');
imagedestroy($mobile_screenshot);

// Desktop screenshot
$desktop_bg = imagecolorallocate($desktop_screenshot, 102, 126, 234);
$desktop_text = imagecolorallocate($desktop_screenshot, 255, 255, 255);
imagefill($desktop_screenshot, 0, 0, $desktop_bg);
imagestring($desktop_screenshot, 5, 580, 350, "Attendance System", $desktop_text);
imagestring($desktop_screenshot, 3, 600, 380, "Desktop View", $desktop_text);
imagepng($desktop_screenshot, 'screenshots/desktop-screenshot.png');
imagedestroy($desktop_screenshot);

echo "✅ Created mobile-screenshot.png (540x720)<br>";
echo "✅ Created desktop-screenshot.png (1280x720)<br>";

echo "<br><h3>🎨 PWA Icons Created Successfully!</h3>";
echo "<p>All required icons and screenshots have been generated. The PWA is now ready for installation.</p>";
echo "<p><strong>Note:</strong> These are placeholder icons. For production, replace them with professionally designed icons.</p>";

echo "<br><a href='setup_complete_system.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>← Back to Setup</a>";
?>
