/**
 * Advanced Device Fingerprinting System
 * Multi-factor passive device identification that survives incognito mode
 */

class DeviceFingerprint {
    constructor() {
        this.fingerprint = {};
        this.touchPatterns = [];
        this.tapRhythms = [];
        this.behaviorScore = 0;
        this.sessionToken = null;
    }

    // Initialize fingerprinting process
    async init(sessionToken = null) {
        this.sessionToken = sessionToken;
        console.log('🔍 Initializing advanced device fingerprinting...');
        
        try {
            // Collect all fingerprint components
            await this.collectBasicInfo();
            await this.collectCanvasFingerprint();
            await this.collectAudioFingerprint();
            await this.collectSensorData();
            await this.collectNetworkInfo();
            
            // Generate composite fingerprint hash
            this.generateDeviceSignatureHash();
            
            // Initialize touch/behavior tracking
            this.initTouchTracking();
            this.initBehaviorTracking();
            
            console.log('✅ Device fingerprinting complete');
            return this.getFingerprint();
        } catch (error) {
            console.error('❌ Fingerprinting error:', error);
            return null;
        }
    }

    // Collect basic device information
    async collectBasicInfo() {
        const nav = navigator;
        this.fingerprint.basic = {
            userAgent: nav.userAgent,
            language: nav.language,
            languages: nav.languages?.join(',') || '',
            platform: nav.platform,
            cookieEnabled: nav.cookieEnabled,
            doNotTrack: nav.doNotTrack,
            hardwareConcurrency: nav.hardwareConcurrency || 0,
            maxTouchPoints: nav.maxTouchPoints || 0,
            vendor: nav.vendor || '',
            vendorSub: nav.vendorSub || '',
            productSub: nav.productSub || ''
        };

        // Screen information
        this.fingerprint.screen = {
            width: screen.width,
            height: screen.height,
            availWidth: screen.availWidth,
            availHeight: screen.availHeight,
            colorDepth: screen.colorDepth,
            pixelDepth: screen.pixelDepth,
            orientation: screen.orientation?.type || 'unknown'
        };

        // Timezone and locale
        this.fingerprint.locale = {
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            timezoneOffset: new Date().getTimezoneOffset(),
            locale: Intl.DateTimeFormat().resolvedOptions().locale
        };
    }

    // Canvas fingerprinting
    async collectCanvasFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = 200;
            canvas.height = 50;
            
            // Draw complex pattern
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillStyle = '#f60';
            ctx.fillRect(125, 1, 62, 20);
            ctx.fillStyle = '#069';
            ctx.fillText('🔐 Device ID: ' + Date.now(), 2, 15);
            ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
            ctx.fillText('Attendance System', 4, 35);
            
            // Add geometric shapes
            ctx.globalCompositeOperation = 'multiply';
            ctx.fillStyle = 'rgb(255,0,255)';
            ctx.beginPath();
            ctx.arc(50, 25, 20, 0, Math.PI * 2, true);
            ctx.closePath();
            ctx.fill();
            
            this.fingerprint.canvas = canvas.toDataURL();
        } catch (error) {
            console.warn('Canvas fingerprinting failed:', error);
            this.fingerprint.canvas = 'unavailable';
        }
    }

    // Audio fingerprinting
    async collectAudioFingerprint() {
        return new Promise((resolve) => {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const analyser = audioContext.createAnalyser();
                const gainNode = audioContext.createGain();
                const scriptProcessor = audioContext.createScriptProcessor(4096, 1, 1);

                gainNode.gain.value = 0; // Mute
                oscillator.type = 'triangle';
                oscillator.frequency.value = 10000;

                oscillator.connect(analyser);
                analyser.connect(scriptProcessor);
                scriptProcessor.connect(gainNode);
                gainNode.connect(audioContext.destination);

                scriptProcessor.onaudioprocess = () => {
                    const array = new Float32Array(analyser.frequencyBinCount);
                    analyser.getFloatFrequencyData(array);
                    
                    let hash = 0;
                    for (let i = 0; i < array.length; i++) {
                        hash += Math.abs(array[i]);
                    }
                    
                    this.fingerprint.audio = hash.toString();
                    oscillator.disconnect();
                    audioContext.close();
                    resolve();
                };

                oscillator.start();
                
                // Timeout fallback
                setTimeout(() => {
                    this.fingerprint.audio = 'timeout';
                    resolve();
                }, 1000);
                
            } catch (error) {
                console.warn('Audio fingerprinting failed:', error);
                this.fingerprint.audio = 'unavailable';
                resolve();
            }
        });
    }

    // Collect sensor data (accelerometer, gyroscope)
    async collectSensorData() {
        this.fingerprint.sensors = {};
        
        // Check for device motion support
        if (window.DeviceMotionEvent) {
            this.fingerprint.sensors.motionSupported = true;
            
            // Request permission for iOS 13+
            if (typeof DeviceMotionEvent.requestPermission === 'function') {
                try {
                    const permission = await DeviceMotionEvent.requestPermission();
                    this.fingerprint.sensors.motionPermission = permission;
                } catch (error) {
                    this.fingerprint.sensors.motionPermission = 'denied';
                }
            }
        } else {
            this.fingerprint.sensors.motionSupported = false;
        }

        // Check for device orientation support
        if (window.DeviceOrientationEvent) {
            this.fingerprint.sensors.orientationSupported = true;
        } else {
            this.fingerprint.sensors.orientationSupported = false;
        }
    }

    // Collect network information
    async collectNetworkInfo() {
        this.fingerprint.network = {};
        
        if (navigator.connection) {
            const conn = navigator.connection;
            this.fingerprint.network = {
                effectiveType: conn.effectiveType || 'unknown',
                downlink: conn.downlink || 0,
                rtt: conn.rtt || 0,
                saveData: conn.saveData || false
            };
        }

        // WebRTC IP detection (if available)
        try {
            const rtc = new RTCPeerConnection({iceServers: []});
            rtc.createDataChannel('');
            rtc.createOffer().then(offer => rtc.setLocalDescription(offer));
            
            rtc.onicecandidate = (event) => {
                if (event.candidate) {
                    const candidate = event.candidate.candidate;
                    const ip = candidate.match(/\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b/);
                    if (ip) {
                        this.fingerprint.network.localIP = ip[0];
                    }
                }
            };
        } catch (error) {
            console.warn('WebRTC IP detection failed:', error);
        }
    }

    // Generate composite Device Signature Hash (DSH)
    generateDeviceSignatureHash() {
        const components = [
            this.fingerprint.basic?.userAgent || '',
            this.fingerprint.screen?.width + 'x' + this.fingerprint.screen?.height || '',
            this.fingerprint.canvas || '',
            this.fingerprint.audio || '',
            this.fingerprint.locale?.timezone || '',
            this.fingerprint.basic?.hardwareConcurrency || '0',
            this.fingerprint.basic?.maxTouchPoints || '0'
        ];

        // Simple hash function
        let hash = 0;
        const str = components.join('|');
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }

        this.fingerprint.deviceSignatureHash = Math.abs(hash).toString(16);
        console.log('🔑 Generated DSH:', this.fingerprint.deviceSignatureHash);
    }

    // Initialize touch pattern tracking
    initTouchTracking() {
        let touchStartTime = 0;
        let touchCount = 0;

        document.addEventListener('touchstart', (e) => {
            touchStartTime = Date.now();
            touchCount++;
            
            const touch = e.touches[0];
            this.touchPatterns.push({
                x: touch.clientX,
                y: touch.clientY,
                pressure: touch.force || 0,
                timestamp: touchStartTime,
                type: 'start'
            });
        });

        document.addEventListener('touchend', (e) => {
            const duration = Date.now() - touchStartTime;
            this.tapRhythms.push(duration);
            
            // Keep only last 10 patterns
            if (this.touchPatterns.length > 10) {
                this.touchPatterns = this.touchPatterns.slice(-10);
            }
            if (this.tapRhythms.length > 10) {
                this.tapRhythms = this.tapRhythms.slice(-10);
            }
        });

        // Mouse events for desktop
        document.addEventListener('mousedown', (e) => {
            touchStartTime = Date.now();
            this.touchPatterns.push({
                x: e.clientX,
                y: e.clientY,
                pressure: 1,
                timestamp: touchStartTime,
                type: 'mouse_start'
            });
        });

        document.addEventListener('mouseup', (e) => {
            const duration = Date.now() - touchStartTime;
            this.tapRhythms.push(duration);
        });
    }

    // Initialize behavior tracking
    initBehaviorTracking() {
        let scrollCount = 0;
        let keyCount = 0;
        let focusCount = 0;

        window.addEventListener('scroll', () => {
            scrollCount++;
            this.behaviorScore += 0.1;
        });

        document.addEventListener('keydown', () => {
            keyCount++;
            this.behaviorScore += 0.2;
        });

        window.addEventListener('focus', () => {
            focusCount++;
            this.behaviorScore += 0.1;
        });

        // Update behavior metrics every 5 seconds
        setInterval(() => {
            this.fingerprint.behavior = {
                scrollCount,
                keyCount,
                focusCount,
                behaviorScore: this.behaviorScore,
                touchPatternCount: this.touchPatterns.length,
                avgTapDuration: this.tapRhythms.length > 0 ? 
                    this.tapRhythms.reduce((a, b) => a + b, 0) / this.tapRhythms.length : 0
            };
        }, 5000);
    }

    // Get complete fingerprint
    getFingerprint() {
        return {
            ...this.fingerprint,
            touchPatterns: this.touchPatterns.slice(-5), // Last 5 patterns
            tapRhythms: this.tapRhythms.slice(-5), // Last 5 rhythms
            sessionToken: this.sessionToken,
            timestamp: Date.now()
        };
    }

    // Save fingerprint to localStorage and server
    async saveFingerprint(userInfo = null) {
        const fp = this.getFingerprint();
        
        // Save to localStorage
        try {
            localStorage.setItem('deviceFingerprint', JSON.stringify(fp));
            localStorage.setItem('deviceSignatureHash', fp.deviceSignatureHash);
        } catch (error) {
            console.warn('localStorage save failed:', error);
        }

        // Save to server
        try {
            const response = await fetch('save_fingerprint.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    fingerprint: fp,
                    userInfo: userInfo,
                    sessionToken: this.sessionToken
                })
            });

            const result = await response.json();
            console.log('💾 Fingerprint saved to server:', result);
            return result;
        } catch (error) {
            console.error('❌ Server save failed:', error);
            return { success: false, error: error.message };
        }
    }

    // Load existing fingerprint
    loadFingerprint() {
        try {
            const stored = localStorage.getItem('deviceFingerprint');
            if (stored) {
                const fp = JSON.parse(stored);
                console.log('📱 Loaded existing fingerprint:', fp.deviceSignatureHash);
                return fp;
            }
        } catch (error) {
            console.warn('Failed to load fingerprint:', error);
        }
        return null;
    }

    // Compare fingerprints for similarity
    compareFingerprints(fp1, fp2) {
        if (!fp1 || !fp2) return 0;

        let matches = 0;
        let total = 0;

        // Compare basic info
        if (fp1.basic && fp2.basic) {
            total += 3;
            if (fp1.basic.userAgent === fp2.basic.userAgent) matches++;
            if (fp1.basic.platform === fp2.basic.platform) matches++;
            if (fp1.basic.hardwareConcurrency === fp2.basic.hardwareConcurrency) matches++;
        }

        // Compare screen
        if (fp1.screen && fp2.screen) {
            total += 2;
            if (fp1.screen.width === fp2.screen.width && fp1.screen.height === fp2.screen.height) matches++;
            if (fp1.screen.colorDepth === fp2.screen.colorDepth) matches++;
        }

        // Compare canvas (exact match required)
        if (fp1.canvas && fp2.canvas) {
            total += 2;
            if (fp1.canvas === fp2.canvas) matches += 2;
        }

        // Compare DSH (most important)
        if (fp1.deviceSignatureHash && fp2.deviceSignatureHash) {
            total += 3;
            if (fp1.deviceSignatureHash === fp2.deviceSignatureHash) matches += 3;
        }

        return total > 0 ? (matches / total) * 100 : 0;
    }
}

// Global instance
window.DeviceFingerprint = DeviceFingerprint;
