<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Enhanced Attendance System Setup</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .setup-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 3px solid #007bff;
        }
        .btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
        }
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Enhanced Attendance System Setup</h1>
        
        <?php
        require_once('db.php');
        date_default_timezone_set('Asia/Thimphu');

        echo "<div class='setup-section'>";
        echo "<h2>📋 System Overview</h2>";
        echo "<p>This setup will configure your attendance system with advanced security features:</p>";
        echo "<div class='feature-list'>";
        
        $features = [
            "🔐 Multi-Factor Device Fingerprinting" => "Canvas, audio, sensor, and network fingerprinting for unique device identification",
            "📱 Progressive Web App (PWA)" => "Offline functionality, app-like experience, and home screen installation",
            "👆 Touch Pattern Biometrics" => "Behavioral analysis of touch patterns, swipe gestures, and tap rhythms",
            "🔗 QR Session Pairing" => "Dynamic QR codes with session tokens and IP verification",
            "📊 Enhanced Attendance Tracking" => "Trust scores, verification levels, and detailed audit trails",
            "🛡️ IP + Device Signature Matching" => "Network-aware authentication that survives incognito mode"
        ];
        
        foreach ($features as $title => $description) {
            echo "<div class='feature-card'>";
            echo "<h4>$title</h4>";
            echo "<p>$description</p>";
            echo "</div>";
        }
        
        echo "</div>";
        echo "</div>";

        // Database setup
        echo "<div class='setup-section'>";
        echo "<h2>🗄️ Database Setup</h2>";
        
        try {
            // Create enhanced tables
            include('setup_enhanced_tables.php');
            
            echo "<div class='success'>";
            echo "<h3>✅ Database Setup Complete!</h3>";
            echo "<p>All enhanced tables have been created successfully.</p>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='error'>";
            echo "<h3>❌ Database Setup Failed</h3>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
        echo "</div>";

        // File verification
        echo "<div class='setup-section'>";
        echo "<h2>📁 File Verification</h2>";
        
        $requiredFiles = [
            'js/device-fingerprint.js' => 'Device fingerprinting library',
            'js/biometric-auth.js' => 'Biometric authentication system',
            'verify_fingerprint.php' => 'Fingerprint verification endpoint',
            'save_fingerprint.php' => 'Fingerprint storage endpoint',
            'manifest.json' => 'PWA manifest file',
            'sw.js' => 'Service worker for offline functionality',
            'offline.html' => 'Offline fallback page',
            'ping.php' => 'Connectivity test endpoint'
        ];
        
        $allFilesExist = true;
        foreach ($requiredFiles as $file => $description) {
            if (file_exists($file)) {
                echo "<div style='color: #28a745;'>✅ $file - $description</div>";
            } else {
                echo "<div style='color: #dc3545;'>❌ $file - $description (MISSING)</div>";
                $allFilesExist = false;
            }
        }
        
        if ($allFilesExist) {
            echo "<div class='success'>";
            echo "<h3>✅ All Required Files Present</h3>";
            echo "</div>";
        } else {
            echo "<div class='error'>";
            echo "<h3>⚠️ Some Files Are Missing</h3>";
            echo "<p>Please ensure all required files are uploaded to your server.</p>";
            echo "</div>";
        }
        echo "</div>";

        // Configuration check
        echo "<div class='setup-section'>";
        echo "<h2>⚙️ Configuration Status</h2>";
        
        // Check if system_config table exists and has data
        $configQuery = "SELECT COUNT(*) as count FROM system_config";
        $configResult = $conn->query($configQuery);
        $configCount = $configResult->fetch_assoc()['count'];
        
        if ($configCount > 0) {
            echo "<div class='success'>";
            echo "<h3>✅ System Configuration Active</h3>";
            echo "<p>Found $configCount configuration settings.</p>";
            echo "</div>";
            
            // Display current config
            $configs = $conn->query("SELECT config_key, config_value, description FROM system_config ORDER BY config_key");
            echo "<h4>Current Settings:</h4>";
            echo "<table style='width: 100%; border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'><th style='padding: 8px; border: 1px solid #dee2e6;'>Setting</th><th style='padding: 8px; border: 1px solid #dee2e6;'>Value</th><th style='padding: 8px; border: 1px solid #dee2e6;'>Description</th></tr>";
            
            while ($config = $configs->fetch_assoc()) {
                echo "<tr>";
                echo "<td style='padding: 8px; border: 1px solid #dee2e6;'><code>" . htmlspecialchars($config['config_key']) . "</code></td>";
                echo "<td style='padding: 8px; border: 1px solid #dee2e6;'>" . htmlspecialchars($config['config_value']) . "</td>";
                echo "<td style='padding: 8px; border: 1px solid #dee2e6;'>" . htmlspecialchars($config['description']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='warning'>";
            echo "<h3>⚠️ No Configuration Found</h3>";
            echo "<p>System configuration table exists but is empty.</p>";
            echo "</div>";
        }
        echo "</div>";

        // Next steps
        echo "<div class='setup-section'>";
        echo "<h2>🎯 Next Steps</h2>";
        echo "<ol>";
        echo "<li><strong>Test QR Code Generation:</strong> Go to the QR Generator and create a dynamic attendance QR code</li>";
        echo "<li><strong>Register Test Device:</strong> Scan the QR code with a mobile device to register its fingerprint</li>";
        echo "<li><strong>Test Attendance:</strong> Try clocking in/out to verify the enhanced security features</li>";
        echo "<li><strong>Check Admin Dashboard:</strong> View the enhanced attendance data and device trust scores</li>";
        echo "<li><strong>Test PWA Features:</strong> Add the app to your home screen and test offline functionality</li>";
        echo "</ol>";
        echo "</div>";

        // Action buttons
        echo "<div class='setup-section' style='text-align: center;'>";
        echo "<h2>🚀 Quick Actions</h2>";
        echo "<a href='qr_generator.php' class='btn btn-success'>📱 Generate QR Code</a>";
        echo "<a href='attendance.php' class='btn'>📋 Test Attendance</a>";
        echo "<a href='admin_dashboard.php' class='btn btn-warning'>👨‍💼 Admin Dashboard</a>";
        echo "<a href='index.php' class='btn'>🏠 Home Page</a>";
        echo "</div>";

        $conn->close();
        ?>
        
        <div class="setup-section">
            <h2>📚 Documentation</h2>
            <h3>🔐 Security Features:</h3>
            <ul>
                <li><strong>Device Fingerprinting:</strong> Collects canvas, audio, sensor, and network data to create unique device signatures</li>
                <li><strong>Trust Scoring:</strong> Devices build trust over time with successful verifications (0-100 scale)</li>
                <li><strong>Session Tokens:</strong> QR codes include time-limited session tokens for enhanced security</li>
                <li><strong>IP Verification:</strong> Ensures attendance is marked from expected network locations</li>
                <li><strong>Behavioral Biometrics:</strong> Analyzes touch patterns, tap rhythms, and interaction behavior</li>
            </ul>
            
            <h3>📱 PWA Features:</h3>
            <ul>
                <li><strong>Offline Support:</strong> Core functionality works without internet connection</li>
                <li><strong>Background Sync:</strong> Data syncs automatically when connection is restored</li>
                <li><strong>App-like Experience:</strong> Can be installed on home screen like a native app</li>
                <li><strong>Push Notifications:</strong> Ready for attendance reminders and alerts</li>
            </ul>
            
            <h3>🛠️ Technical Details:</h3>
            <pre>
Database Tables:
- device_fingerprints: Stores device signatures and trust scores
- scan_sessions: Manages QR code session tokens
- enhanced_attendance: Detailed attendance with security data
- device_staff_links: Links devices to staff members
- touch_patterns: Behavioral biometric data
- system_config: Configurable security parameters

JavaScript Libraries:
- DeviceFingerprint: Multi-factor device identification
- BiometricAuth: Touch pattern and behavioral analysis

Security Endpoints:
- save_fingerprint.php: Register new devices
- verify_fingerprint.php: Authenticate existing devices
- ping.php: Connectivity testing
            </pre>
        </div>
    </div>
</body>
</html>
