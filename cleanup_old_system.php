<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧹 System Cleanup - Enhanced Attendance</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .cleanup-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            border: 1px solid #dee2e6;
            font-size: 14px;
        }
        .btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 System Cleanup & Compatibility Check</h1>
        
        <?php
        require_once('db.php');
        date_default_timezone_set('Asia/Thimphu');

        echo "<div class='cleanup-section'>";
        echo "<h2>📋 Cleanup Overview</h2>";
        echo "<p>This script removes old conflicting authentication methods and ensures compatibility between the new enhanced security system and existing phone-based authentication.</p>";
        echo "</div>";

        // Check for old files that might conflict
        echo "<div class='cleanup-section'>";
        echo "<h2>📁 File Cleanup</h2>";
        
        $old_files_to_check = [
            'verify.php' => 'Old verification script',
            'fetch_user_by_phone.php' => 'Old phone fetching script',
            'generate_qr.php' => 'Old QR generation script',
            'debug_attendance.php' => 'Old debug script',
            'old_attendance.php' => 'Backup attendance file',
            'backup_attendance.php' => 'Backup attendance file'
        ];
        
        $files_removed = 0;
        foreach ($old_files_to_check as $file => $description) {
            if (file_exists($file)) {
                if (unlink($file)) {
                    echo "<div style='color: #28a745;'>✅ Removed: $file - $description</div>";
                    $files_removed++;
                } else {
                    echo "<div style='color: #dc3545;'>❌ Failed to remove: $file - $description</div>";
                }
            } else {
                echo "<div style='color: #6c757d;'>ℹ️ Not found: $file - $description (already clean)</div>";
            }
        }
        
        if ($files_removed > 0) {
            echo "<div class='success'>";
            echo "<h3>✅ File Cleanup Complete</h3>";
            echo "<p>Removed $files_removed old conflicting files.</p>";
            echo "</div>";
        } else {
            echo "<div class='info'>";
            echo "<h3>ℹ️ No Old Files Found</h3>";
            echo "<p>System is already clean - no conflicting files detected.</p>";
            echo "</div>";
        }
        echo "</div>";

        // Database cleanup
        echo "<div class='cleanup-section'>";
        echo "<h2>🗄️ Database Compatibility Check</h2>";
        
        try {
            // Check if enhanced tables exist
            $enhanced_tables = [
                'device_fingerprints',
                'scan_sessions', 
                'enhanced_attendance',
                'device_staff_links',
                'touch_patterns',
                'system_config'
            ];
            
            $tables_exist = 0;
            foreach ($enhanced_tables as $table) {
                $result = $conn->query("SHOW TABLES LIKE '$table'");
                if ($result->num_rows > 0) {
                    echo "<div style='color: #28a745;'>✅ Enhanced table exists: $table</div>";
                    $tables_exist++;
                } else {
                    echo "<div style='color: #dc3545;'>❌ Missing enhanced table: $table</div>";
                }
            }
            
            // Check original tables
            $original_tables = ['staff', 'attendance'];
            foreach ($original_tables as $table) {
                $result = $conn->query("SHOW TABLES LIKE '$table'");
                if ($result->num_rows > 0) {
                    echo "<div style='color: #28a745;'>✅ Original table exists: $table</div>";
                } else {
                    echo "<div style='color: #dc3545;'>❌ Missing original table: $table</div>";
                }
            }
            
            if ($tables_exist === count($enhanced_tables)) {
                echo "<div class='success'>";
                echo "<h3>✅ Database Schema Complete</h3>";
                echo "<p>All enhanced tables are present and ready.</p>";
                echo "</div>";
            } else {
                echo "<div class='warning'>";
                echo "<h3>⚠️ Incomplete Database Schema</h3>";
                echo "<p>Some enhanced tables are missing. Run setup_enhanced_tables.php to create them.</p>";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='error'>";
            echo "<h3>❌ Database Check Failed</h3>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
        echo "</div>";

        // Authentication method compatibility
        echo "<div class='cleanup-section'>";
        echo "<h2>🔐 Authentication Method Analysis</h2>";
        
        try {
            // Count staff with phone registrations
            $phone_users = $conn->query("SELECT COUNT(*) as count FROM staff WHERE phone_model IS NOT NULL AND unique_id IS NOT NULL")->fetch_assoc()['count'];
            
            // Count devices with fingerprints
            $fingerprint_devices = 0;
            if ($conn->query("SHOW TABLES LIKE 'device_fingerprints'")->num_rows > 0) {
                $fingerprint_devices = $conn->query("SELECT COUNT(*) as count FROM device_fingerprints WHERE is_active = TRUE")->fetch_assoc()['count'];
            }
            
            // Count linked devices
            $linked_devices = 0;
            if ($conn->query("SHOW TABLES LIKE 'device_staff_links'")->num_rows > 0) {
                $linked_devices = $conn->query("SELECT COUNT(*) as count FROM device_staff_links")->fetch_assoc()['count'];
            }
            
            echo "<h3>📊 Current Authentication Status:</h3>";
            echo "<ul>";
            echo "<li><strong>Phone-based users:</strong> $phone_users (legacy authentication)</li>";
            echo "<li><strong>Fingerprinted devices:</strong> $fingerprint_devices (enhanced security)</li>";
            echo "<li><strong>Linked devices:</strong> $linked_devices (staff-device associations)</li>";
            echo "</ul>";
            
            if ($fingerprint_devices > 0) {
                echo "<div class='success'>";
                echo "<h3>✅ Enhanced Security Active</h3>";
                echo "<p>The new device fingerprinting system is operational with $fingerprint_devices registered devices.</p>";
                echo "</div>";
            }
            
            if ($phone_users > 0) {
                echo "<div class='info'>";
                echo "<h3>ℹ️ Legacy Compatibility Maintained</h3>";
                echo "<p>$phone_users users can still use phone-based authentication as a fallback method.</p>";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='error'>";
            echo "<h3>❌ Authentication Analysis Failed</h3>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
        echo "</div>";

        // System compatibility summary
        echo "<div class='cleanup-section'>";
        echo "<h2>🎯 Compatibility Summary</h2>";
        echo "<h3>✅ What Works Now:</h3>";
        echo "<ul>";
        echo "<li><strong>Enhanced Security:</strong> Device fingerprinting with session tokens</li>";
        echo "<li><strong>Legacy Support:</strong> Phone model + unique ID authentication</li>";
        echo "<li><strong>Progressive Web App:</strong> Offline functionality and app installation</li>";
        echo "<li><strong>Cross-Platform:</strong> Works on both iOS and Android devices</li>";
        echo "<li><strong>Behavioral Biometrics:</strong> Touch pattern analysis and trust scoring</li>";
        echo "</ul>";
        
        echo "<h3>📱 Platform Compatibility:</h3>";
        echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0;'>";
        echo "<div style='background: white; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6;'>";
        echo "<h4>🍎 iOS Support</h4>";
        echo "<ul style='margin: 0; padding-left: 20px;'>";
        echo "<li>Safari 11.1+ PWA support</li>";
        echo "<li>Device fingerprinting via web APIs</li>";
        echo "<li>Touch event biometrics</li>";
        echo "<li>'Add to Home Screen' functionality</li>";
        echo "</ul>";
        echo "</div>";
        echo "<div style='background: white; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6;'>";
        echo "<h4>🤖 Android Support</h4>";
        echo "<ul style='margin: 0; padding-left: 20px;'>";
        echo "<li>Chrome 57+ full PWA support</li>";
        echo "<li>Enhanced device fingerprinting</li>";
        echo "<li>Advanced sensor access</li>";
        echo "<li>Native app-like installation</li>";
        echo "</ul>";
        echo "</div>";
        echo "</div>";
        
        echo "<h3>🔄 Migration Path:</h3>";
        echo "<ol>";
        echo "<li><strong>Immediate:</strong> Both authentication methods work simultaneously</li>";
        echo "<li><strong>Gradual:</strong> Users naturally migrate to enhanced security when scanning QR codes</li>";
        echo "<li><strong>Optional:</strong> Admins can disable phone-based auth once all users are migrated</li>";
        echo "</ol>";
        echo "</div>";

        // Next steps
        echo "<div class='cleanup-section'>";
        echo "<h2>🚀 Next Steps</h2>";
        echo "<ol>";
        echo "<li><strong>Test Both Methods:</strong> Verify phone-based and fingerprint authentication work</li>";
        echo "<li><strong>Generate QR Codes:</strong> Create dynamic QR codes for enhanced security</li>";
        echo "<li><strong>User Migration:</strong> Encourage users to scan QR codes to register devices</li>";
        echo "<li><strong>Monitor Trust Scores:</strong> Watch device trust levels in admin dashboard</li>";
        echo "<li><strong>PWA Installation:</strong> Test 'Add to Home Screen' on mobile devices</li>";
        echo "</ol>";
        echo "</div>";

        // Action buttons
        echo "<div class='cleanup-section' style='text-align: center;'>";
        echo "<h2>🎮 Quick Actions</h2>";
        echo "<a href='setup_complete_system.php' class='btn'>🔧 Complete Setup</a>";
        echo "<a href='qr_generator.php' class='btn'>📱 Generate QR Code</a>";
        echo "<a href='attendance.php' class='btn'>📋 Test Attendance</a>";
        echo "<a href='admin_dashboard.php' class='btn'>👨‍💼 Admin Dashboard</a>";
        echo "</div>";

        $conn->close();
        ?>
        
        <div class="cleanup-section">
            <h2>📚 Technical Notes</h2>
            <h3>🔧 How Both Systems Work Together:</h3>
            <pre>
Authentication Priority:
1. Session Token + Device Fingerprint (Enhanced Security)
   - User scans dynamic QR code
   - Device fingerprint collected automatically
   - Trust score builds over time
   - Works in incognito mode

2. Phone Model + Unique ID (Legacy Fallback)
   - User registered via register_phone.php
   - Simple phone model matching
   - Backward compatibility maintained
   - Still secure for local network use

The system automatically chooses the best available method.
            </pre>
            
            <h3>🛡️ Security Benefits:</h3>
            <ul>
                <li><strong>No Conflicts:</strong> Both methods can coexist safely</li>
                <li><strong>Gradual Migration:</strong> Users upgrade to enhanced security naturally</li>
                <li><strong>Fallback Protection:</strong> System remains functional if one method fails</li>
                <li><strong>Future-Proof:</strong> Easy to disable legacy method when ready</li>
            </ul>
        </div>
    </div>
</body>
</html>
