<?php
require_once('db.php');  // Your DB connection file

header('Content-Type: application/json');

// Debug logging function
function debug_log($message) {
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents('debug_log.txt', "[$timestamp] $message\n", FILE_APPEND);
}

$data = json_decode(file_get_contents("php://input"), true);
$phone_model = trim($data["phone_model"] ?? '');

debug_log("fetch_user_by_phone.php - Looking for phone model: " . $phone_model);

$response = ['success' => false];

if ($phone_model !== '') {
    $stmt = $conn->prepare("SELECT id, full_name, eid_cid_permit FROM staff WHERE phone_model = ?");
    $stmt->bind_param("s", $phone_model);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($row = $result->fetch_assoc()) {
        debug_log("fetch_user_by_phone.php - Found staff: " . $row['full_name']);
        $response = [
            'success' => true,
            'staff_id' => $row['id'],
            'full_name' => $row['full_name'],
            'eid' => $row['eid_cid_permit']
        ];
    } else {
        debug_log("fetch_user_by_phone.php - No staff found for phone model: " . $phone_model);
    }
}

echo json_encode($response);
