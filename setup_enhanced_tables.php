<?php
require_once('db.php');
date_default_timezone_set('Asia/Thimphu');

echo "<h2>🔧 Setting up Enhanced Attendance System Tables</h2>";

try {
    // Create scan_sessions table
    $scanSessionsTable = "
    CREATE TABLE IF NOT EXISTS scan_sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        session_token VARCHAR(255) UNIQUE NOT NULL,
        client_ip VARCHAR(45) NOT NULL,
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NOT NULL,
        used_at TIMESTAMP NULL,
        is_used BOOLEAN DEFAULT FALSE,
        INDEX idx_token (session_token),
        INDEX idx_ip (client_ip),
        INDEX idx_expires (expires_at)
    )";
    
    if ($conn->query($scanSessionsTable)) {
        echo "✅ scan_sessions table created/verified<br>";
    } else {
        echo "❌ Error creating scan_sessions table: " . $conn->error . "<br>";
    }

    // Create device_fingerprints table
    $deviceFingerprintsTable = "
    CREATE TABLE IF NOT EXISTS device_fingerprints (
        id INT AUTO_INCREMENT PRIMARY KEY,
        device_signature_hash VARCHAR(255) UNIQUE NOT NULL,
        fingerprint_data JSON NOT NULL,
        user_info JSON,
        client_ip VARCHAR(45),
        session_token VARCHAR(255),
        first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        trust_score DECIMAL(5,2) DEFAULT 10.00,
        verification_count INT DEFAULT 0,
        failed_attempts INT DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        notes TEXT,
        INDEX idx_dsh (device_signature_hash),
        INDEX idx_ip (client_ip),
        INDEX idx_session (session_token),
        INDEX idx_trust (trust_score),
        INDEX idx_active (is_active)
    )";
    
    if ($conn->query($deviceFingerprintsTable)) {
        echo "✅ device_fingerprints table created/verified<br>";
    } else {
        echo "❌ Error creating device_fingerprints table: " . $conn->error . "<br>";
    }

    // Create device_staff_links table
    $deviceStaffLinksTable = "
    CREATE TABLE IF NOT EXISTS device_staff_links (
        id INT AUTO_INCREMENT PRIMARY KEY,
        device_id INT NOT NULL,
        staff_id INT NOT NULL,
        linked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_used TIMESTAMP NULL,
        is_primary BOOLEAN DEFAULT FALSE,
        trust_level ENUM('low', 'medium', 'high') DEFAULT 'medium',
        usage_count INT DEFAULT 0,
        UNIQUE KEY unique_device_staff (device_id, staff_id),
        INDEX idx_device (device_id),
        INDEX idx_staff (staff_id),
        INDEX idx_primary (is_primary)
    )";
    
    if ($conn->query($deviceStaffLinksTable)) {
        echo "✅ device_staff_links table created/verified<br>";
    } else {
        echo "❌ Error creating device_staff_links table: " . $conn->error . "<br>";
    }

    // Create touch_patterns table for behavioral biometrics
    $touchPatternsTable = "
    CREATE TABLE IF NOT EXISTS touch_patterns (
        id INT AUTO_INCREMENT PRIMARY KEY,
        device_id INT NOT NULL,
        staff_id INT,
        pattern_data JSON NOT NULL,
        pattern_type ENUM('touch', 'mouse', 'swipe', 'tap') DEFAULT 'touch',
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        session_token VARCHAR(255),
        confidence_score DECIMAL(5,2) DEFAULT 0.00,
        INDEX idx_device (device_id),
        INDEX idx_staff (staff_id),
        INDEX idx_type (pattern_type),
        INDEX idx_recorded (recorded_at)
    )";
    
    if ($conn->query($touchPatternsTable)) {
        echo "✅ touch_patterns table created/verified<br>";
    } else {
        echo "❌ Error creating touch_patterns table: " . $conn->error . "<br>";
    }

    // Create enhanced_attendance table with device tracking
    $enhancedAttendanceTable = "
    CREATE TABLE IF NOT EXISTS enhanced_attendance (
        id INT AUTO_INCREMENT PRIMARY KEY,
        staff_id INT NOT NULL,
        device_id INT,
        session_token VARCHAR(255),
        date DATE NOT NULL,
        clock_in TIME,
        clock_out TIME,
        clock_in_ip VARCHAR(45),
        clock_out_ip VARCHAR(45),
        clock_in_fingerprint_score DECIMAL(5,2),
        clock_out_fingerprint_score DECIMAL(5,2),
        total_hours DECIMAL(4,2),
        verification_level ENUM('low', 'medium', 'high') DEFAULT 'medium',
        anomaly_flags JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_staff_date (staff_id, date),
        INDEX idx_device (device_id),
        INDEX idx_session (session_token),
        INDEX idx_verification (verification_level)
    )";
    
    if ($conn->query($enhancedAttendanceTable)) {
        echo "✅ enhanced_attendance table created/verified<br>";
    } else {
        echo "❌ Error creating enhanced_attendance table: " . $conn->error . "<br>";
    }

    // Create system_config table for settings
    $systemConfigTable = "
    CREATE TABLE IF NOT EXISTS system_config (
        id INT AUTO_INCREMENT PRIMARY KEY,
        config_key VARCHAR(100) UNIQUE NOT NULL,
        config_value TEXT NOT NULL,
        description TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_key (config_key)
    )";
    
    if ($conn->query($systemConfigTable)) {
        echo "✅ system_config table created/verified<br>";
    } else {
        echo "❌ Error creating system_config table: " . $conn->error . "<br>";
    }

    // Insert default configuration values
    $defaultConfigs = [
        ['min_fingerprint_similarity', '70', 'Minimum fingerprint similarity score required for verification'],
        ['min_trust_score', '5.0', 'Minimum device trust score required for attendance'],
        ['session_timeout_minutes', '10', 'QR code session timeout in minutes'],
        ['max_failed_attempts', '5', 'Maximum failed verification attempts before device lockout'],
        ['trust_score_increment', '0.5', 'Trust score increase on successful verification'],
        ['trust_score_decrement', '2.0', 'Trust score decrease on failed verification'],
        ['enable_behavioral_biometrics', '1', 'Enable touch pattern and behavioral analysis'],
        ['local_network_range', '***********/24', 'Allowed local network IP range']
    ];

    foreach ($defaultConfigs as $config) {
        $stmt = $conn->prepare("
            INSERT INTO system_config (config_key, config_value, description) 
            VALUES (?, ?, ?) 
            ON DUPLICATE KEY UPDATE description = VALUES(description)
        ");
        $stmt->bind_param("sss", $config[0], $config[1], $config[2]);
        $stmt->execute();
    }
    
    echo "✅ Default system configuration inserted<br>";

    // Clean up old expired sessions
    $cleanupStmt = $conn->prepare("DELETE FROM scan_sessions WHERE expires_at < NOW()");
    $cleanupStmt->execute();
    $deletedSessions = $conn->affected_rows;
    
    echo "🧹 Cleaned up $deletedSessions expired sessions<br>";

    echo "<br><h3>✅ Enhanced Attendance System Setup Complete!</h3>";
    echo "<p>The system now includes:</p>";
    echo "<ul>";
    echo "<li>🔐 Advanced device fingerprinting with trust scores</li>";
    echo "<li>📱 Session-based QR code authentication</li>";
    echo "<li>👆 Touch pattern and behavioral biometrics</li>";
    echo "<li>🔗 Device-to-staff linking system</li>";
    echo "<li>📊 Enhanced attendance tracking with verification levels</li>";
    echo "<li>⚙️ Configurable security parameters</li>";
    echo "</ul>";

} catch (Exception $e) {
    echo "❌ Setup error: " . $e->getMessage() . "<br>";
}

$conn->close();
?>
