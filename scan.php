<?php
// Enhanced scan handler with session token validation
require_once('db.php');
date_default_timezone_set('Asia/Thimphu');

// Debug logging function
function debug_log($message) {
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents('debug_log.txt', "[$timestamp] $message\n", FILE_APPEND);
}

$client_ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

// Check if token provided (from dynamic QR)
$provided_token = $_GET['token'] ?? null;

if ($provided_token) {
    // Validate existing session token
    $stmt = $conn->prepare("
        SELECT session_token, client_ip, created_at, expires_at
        FROM scan_sessions
        WHERE session_token = ? AND expires_at > NOW() AND is_used = FALSE
    ");
    $stmt->bind_param("s", $provided_token);
    $stmt->execute();
    $session = $stmt->get_result()->fetch_assoc();

    if ($session) {
        // Mark session as used
        $updateStmt = $conn->prepare("UPDATE scan_sessions SET is_used = TRUE, used_at = NOW() WHERE session_token = ?");
        $updateStmt->bind_param("s", $provided_token);
        $updateStmt->execute();

        debug_log("scan.php - Valid session token used: $provided_token from IP: $client_ip");

        // Redirect with validated session
        header("Location: attendance.php?session=" . $provided_token);
        exit();
    } else {
        debug_log("scan.php - Invalid/expired session token: $provided_token from IP: $client_ip");

        // Redirect to error page or generate new session
        header("Location: attendance.php?error=invalid_session");
        exit();
    }
} else {
    // No token provided - generate new session (fallback for permanent QR)
    $session_token = bin2hex(random_bytes(16));

    debug_log("scan.php - No token provided, generating new session: $session_token from IP: $client_ip");

    // Store new session in database
    $stmt = $conn->prepare("INSERT INTO scan_sessions (session_token, client_ip, user_agent, expires_at) VALUES (?, ?, ?, DATE_ADD(NOW(), INTERVAL 10 MINUTE))");
    $stmt->bind_param("sss", $session_token, $client_ip, $user_agent);
    $stmt->execute();

    // Redirect with new session token
    header("Location: attendance.php?session=" . $session_token);
    exit();
}
?>
