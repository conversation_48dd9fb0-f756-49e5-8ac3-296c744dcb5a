<?php
// Debug script to check attendance data
require_once('db.php');
date_default_timezone_set('Asia/Thimphu');

$today = date("Y-m-d");

echo "<h2>Debug Attendance Data for $today</h2>";

// Check all attendance records for today
$result = $conn->query("SELECT a.*, s.full_name FROM attendance a JOIN staff s ON a.staff_id = s.id WHERE a.date = '$today' ORDER BY a.id DESC");

echo "<h3>All Attendance Records for Today:</h3>";
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>ID</th><th>Staff Name</th><th>Date</th><th>Clock In</th><th>Clock Out</th><th>Created</th></tr>";

while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['id'] . "</td>";
    echo "<td>" . $row['full_name'] . "</td>";
    echo "<td>" . $row['date'] . "</td>";
    echo "<td>" . ($row['clock_in'] ?? 'NULL') . "</td>";
    echo "<td>" . ($row['clock_out'] ?? 'NULL') . "</td>";
    echo "<td>" . ($row['created_at'] ?? 'N/A') . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check specific user - Choki Wangmo
echo "<h3>Choki Wangmo's Records:</h3>";
$result2 = $conn->query("SELECT a.*, s.full_name FROM attendance a JOIN staff s ON a.staff_id = s.id WHERE s.full_name = 'Choki Wangmo' AND a.date = '$today' ORDER BY a.id DESC");

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>ID</th><th>Staff Name</th><th>Date</th><th>Clock In</th><th>Clock Out</th></tr>";

while ($row = $result2->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['id'] . "</td>";
    echo "<td>" . $row['full_name'] . "</td>";
    echo "<td>" . $row['date'] . "</td>";
    echo "<td>" . ($row['clock_in'] ?? 'NULL') . "</td>";
    echo "<td>" . ($row['clock_out'] ?? 'NULL') . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check staff table
echo "<h3>Staff Records:</h3>";
$result3 = $conn->query("SELECT id, full_name, phone_model, unique_id FROM staff WHERE phone_model IS NOT NULL");

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>ID</th><th>Full Name</th><th>Phone Model</th><th>Unique ID</th></tr>";

while ($row = $result3->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['id'] . "</td>";
    echo "<td>" . $row['full_name'] . "</td>";
    echo "<td>" . $row['phone_model'] . "</td>";
    echo "<td>" . $row['unique_id'] . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<p>Current time: " . date('Y-m-d H:i:s') . "</p>";
?>
