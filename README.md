# Role-Based Attendance System with Admin Management

## 🚀 What's New - Role-Based System!

This implementation includes a complete role-based system with admin management capabilities and streamlined user experience:

### ✅ Fixed Issues
1. **Missing database connection file** - Created `db.php`
2. **Column name inconsistencies** - Fixed `name` vs `full_name`, `eid` vs `eid_cid_permit`
3. **Static QR code generation** - Now dynamic with expiration and security tokens
4. **Phone model detection problems** - Enhanced JavaScript detection for iOS and Android
5. **Device token validation issues** - Dynamic token generation and validation

### 🆕 New Features - Role-Based System
1. **Role-Based Access Control** - Different interfaces for regular users vs admins
2. **Admin User Management** - Admins can remove user registrations and manage the system
3. **Restricted Admin Access** - Only <PERSON><PERSON> and <PERSON><PERSON>zin Yuedsel have admin privileges
4. **Simplified User Interface** - Regular users only see Registration and Attendance
5. **2-QR System** - Only 2 QR codes needed total (Registration + Attendance)
6. **Unique ID System** - Each user gets a unique identifier when registering their phone
7. **Enhanced Security** - One registration per user, phone model validation
8. **Mobile-Responsive Design** - Professional UI that works on all devices
9. **Automatic User Identification** - System identifies users by phone model + unique ID

## 📁 Files Created/Modified

### New Files:
- `db.php` - Database connection file
- `register_phone.php` - Phone registration interface
- `qr_generator.php` - QR code generation interface
- `setup_database.php` - Database setup and column creation
- `index.php` - Main dashboard and navigation
- `README.md` - This documentation

### Modified Files:
- `fetch_user_by_phone.php` - Fixed column names and added debug logging
- `scan.php` - Dynamic QR validation with expiration
- `attendance.php` - Enhanced phone detection and unique ID display
- `verify.php` - Fixed column name inconsistencies

## 🗄️ Database Changes

The system now includes these additional columns:
- `staff.unique_id` - Unique identifier for each user
- `staff.phone_model` - Registered phone model
- `staff.device_token` - Device security token
- `staff.created_at` - Registration timestamp
- `attendance.device_token` - Device token for attendance records
- `attendance.clock_in_phone_model` - Phone model used for clock in
- `attendance.clock_out_phone_model` - Phone model used for clock out

## 🧪 Testing Instructions - Simplified 2-QR System

### Step 1: Database Setup
1. Visit: `http://172.31.18.161/attendance/setup_database.php`
2. This will create all required database columns
3. Verify that all columns are created successfully

### Step 2: Generate the 2 QR Codes
1. Visit: `http://172.31.18.161/attendance/qr_generator.php`
2. Click "Generate Registration QR Code" - save/print this QR code
3. Click "Generate Attendance QR Code" - save/print this QR code
4. **Result:** You now have 2 permanent QR codes (no expiry needed)

### Step 3: Test User Registration
1. Scan the **Registration QR Code** with your phone
2. This opens the registration page automatically
3. The system detects your phone model automatically
4. Select your name from the dropdown
5. Click "Register Phone Model"
6. Note the unique ID that gets assigned to you

### Step 4: Test Attendance
1. Scan the **Attendance QR Code** with your phone
2. This opens the attendance page automatically
3. System identifies you by your registered phone model + unique ID
4. You should see your name, EID/CID/Permit, and Unique ID displayed
5. Click "Clock In" to record attendance
6. Later, scan the same Attendance QR Code again and click "Clock Out"

### Step 5: Test Security Features
1. Try using a different phone (not registered) - should show registration link
2. Try clocking in twice - should show "already clocked in" message
3. Try clocking out without clocking in - should show warning

### Step 6: Verify Results
1. Check the attendance table on the attendance page
2. Visit: `http://172.31.18.161/attendance/admin_dashboard.php` for full reports
3. Check `debug_log.txt` for system logs

## 🔍 Troubleshooting

### Phone Not Recognized?
1. Check `debug_log.txt` to see what phone model is being detected
2. Visit `register_phone.php` to see the "Currently Registered Phones" table
3. Make sure you're using the exact same phone you registered with

### QR Code Issues?
1. QR codes expire after 1 hour - generate a new one
2. Make sure you're on the internal network (172.31.18.161)
3. Check that the QR code was generated successfully

### Database Issues?
1. Run `setup_database.php` to ensure all columns exist
2. Check database connection in `db.php`
3. Verify staff table has the required data

## 🌟 Key Features

### Unique ID System
- Each user gets a unique ID like `UID-A1B2C3D4-20241201`
- Format: `UID-[8 hex chars]-[YYYYMMDD]`
- Displayed on attendance page and stored in database
- Helps with user identification and tracking

### Enhanced Phone Detection
- Better support for iOS devices (detects iOS version)
- Improved Android detection (detects Android version)
- Includes screen resolution for additional uniqueness
- Consistent detection across different browsers

### Dynamic QR Codes
- Each QR code has a unique device token
- QR codes expire after 1 hour for security
- Support for both general and staff-specific QR codes
- Proper validation and error handling

### Debug Logging
- All system actions are logged to `debug_log.txt`
- Includes timestamps and detailed information
- Helps with troubleshooting and system monitoring

## 📱 Usage Limits

- Each device can only be used twice per day (once for clock in, once for clock out)
- Phone model must match the registered device
- QR codes expire after 1 hour
- Device tokens are validated for security

## 🔗 Navigation

The system includes a main dashboard at `index.php` with links to all components:
- Database Setup
- Phone Registration
- QR Generator
- Attendance Page
- Admin Dashboard

## 📊 System Flow

1. **Setup** → Run database setup
2. **Register** → Register phone model (gets unique ID)
3. **Generate** → Create QR code
4. **Scan** → Use phone to scan QR code
5. **Attend** → Clock in/out with registered device

## 🎯 Success Criteria

The system is working correctly when:
1. Users can register their phones and get unique IDs
2. QR codes can be generated and scanned successfully
3. Phone models are detected consistently
4. Attendance records are created with proper device tracking
5. Debug logs show successful operations
6. Admin dashboard displays comprehensive reports

## 🔒 Security Features

### One Registration Per User
- **Prevents Multiple Registrations**: Each user can only register once with one specific phone
- **Phone Model Validation**: Each phone model can only be registered to one user
- **Clear Error Messages**: Users get helpful feedback if they try to register again or use an already registered phone

### Enhanced User Identification
- **Unique ID Display**: UID is prominently displayed in attendance system for verification
- **Phone Model Matching**: System validates exact phone model match for security
- **Daily Limits**: Each user can only clock in once and clock out once per day

## 📱 Mobile-Responsive Design

### Modern UI/UX
- **Gradient Backgrounds**: Beautiful modern design with smooth gradients
- **Mobile-First**: Fully responsive design that works perfectly on all devices
- **Touch-Friendly**: Large buttons and touch targets for mobile users
- **Clean Typography**: Modern font stack with excellent readability

### Mobile Optimizations
- **Responsive Grid**: Information displays in mobile-friendly grid layout
- **Collapsible Tables**: Tables scroll horizontally on mobile with sticky headers
- **Large Touch Targets**: Buttons are sized appropriately for touch interaction
- **Optimized Images**: QR codes scale properly on all screen sizes

## 🎨 User Experience Improvements

### Visual Enhancements
- **Color-Coded Information**: Different colors for different types of information
- **Status Indicators**: Visual indicators for clock in/out status
- **Prominent UID Display**: Unique ID is highlighted and easy to identify
- **Professional Layout**: Clean, organized layout with proper spacing

### Better Navigation
- **Consistent Navigation**: Same navigation links across all pages
- **Breadcrumb-Style**: Clear indication of current page and available actions
- **Quick Access**: Easy access to all system functions from any page

---

**System Version:** 3.0 - Mobile-Responsive with Enhanced Security
**Last Updated:** December 2024
